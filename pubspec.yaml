name: shridattmandir
description: <PERSON> <PERSON><PERSON>dir Mobile Application
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.8.3+28
environment:
  sdk: '>=3.3.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

#* +-------------------------+
#* |     User Interface      |
#* +-------------------------+
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.3
  confetti: ^0.8.0
  overlay_support: ^2.1.0
  audio_video_progress_bar: ^2.0.3
  very_good_infinite_list: ^0.9.0
  pinput: ^5.0.1
  shimmer: ^3.0.0

#* +-------------------------+
#* |        Firebase         |
#* +-------------------------+
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  cloud_firestore: ^5.6.8
  firebase_crashlytics: ^4.3.6
  firebase_messaging: ^15.2.6
  firebase_remote_config: ^5.4.4
  firebase_storage: ^12.4.6
  firebase_analytics: ^11.4.6

#* +-------------------------+
#* |     State Management    |
#* +-------------------------+
  flutter_bloc: ^9.1.1
  equatable: ^2.0.7

#* +-------------------------+
#* |       Networking        |
#* +-------------------------+
  http: ^1.3.0

#* +-------------------------+
#* |        Utilities        |
#* +-------------------------+

# Image Utils
  flutter_svg: ^2.1.0
  extended_image: ^10.0.1

# Other Utils
  youtube_player_flutter: ^9.1.1
  intl: ^0.20.2
  webview_flutter: ^4.13.0
  url_launcher: ^6.3.1
  flutter_email_sender: ^7.0.0
  flutter_local_notifications: ^19.2.1
  just_audio: ^0.10.3
  just_audio_background: ^0.0.1-beta.17
  flutter_secure_storage: ^9.2.4

# Native Device Info Utils
  device_info_plus: ^11.4.0
  package_info_plus: ^8.3.0
  file_picker: ^10.1.9
  flutter_app_badger:
    git:
      url: https://github.com/bitsydarel/flutter_app_badger.git

#* +--------------------------+
#* |       IN-APP SDK'S       |
#* +--------------------------+
  purchases_flutter: ^8.8.1
  in_app_review: ^2.0.10
  in_app_update: ^4.2.3

# dependency_overrides:
#   flutter_inappwebview: ^5.8.0

# # !https://github.com/miguelpruivo/flutter_file_picker/issues/1531
#   win32: ^5.5.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  flutter_gen_runner: ^5.10.0
  build_runner: ^2.4.15


flutter_gen:
  output: lib/src/core/assets/
  line_length: 80
 
  integrations:
    flutter_svg: true

  font:
    enabled: true
    outputs:
      class_name: SdmFonts


flutter:
  uses-material-design: true
  assets:
    - assets/sdm-icons/
    - assets/sdm-images/
  
  fonts:
    - family: Montserrat
      fonts:
       - asset: assets/sdm-fonts/Montserrat-Regular.ttf
       - asset: assets/sdm-fonts/Montserrat-Medium.ttf
         weight: 600
       - asset: assets/sdm-fonts/Montserrat-Bold.ttf
         weight: 700
       - asset: assets/sdm-fonts/Montserrat-Black.ttf
         weight: 900
       - asset: assets/sdm-fonts/Montserrat-Italic.ttf
         style: italic
